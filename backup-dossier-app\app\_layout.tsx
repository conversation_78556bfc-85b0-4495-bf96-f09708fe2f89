import React from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Platform } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';

// Safe imports with fallbacks for your contexts
let AuthProvider, FinanceProvider, ThemeProvider, useFrameworkReady;

try {
  const auth = require('@/contexts/AuthContext');
  AuthProvider = auth.AuthProvider;
} catch (e) {
  console.log('AuthContext not loaded, using fallback');
  AuthProvider = ({ children }: any) => React.createElement(React.Fragment, null, children);
}

try {
  const finance = require('@/contexts/FinanceContext');
  FinanceProvider = finance.FinanceProvider;
} catch (e) {
  console.log('FinanceContext not loaded, using fallback');
  FinanceProvider = ({ children }: any) => React.createElement(React.Fragment, null, children);
}

try {
  const theme = require('@/contexts/ThemeContext');
  ThemeProvider = theme.ThemeProvider;
} catch (e) {
  console.log('ThemeContext not loaded, using fallback');
  ThemeProvider = ({ children }: any) => React.createElement(React.Fragment, null, children);
}

try {
  const framework = require('@/hooks/useFrameworkReady');
  useFrameworkReady = framework.useFrameworkReady;
} catch (e) {
  useFrameworkReady = () => {};
}

function RootLayout() {
  // Safe hook call
  React.useEffect(() => {
    try {
      if (useFrameworkReady) {
        useFrameworkReady();
      }
    } catch (e) {
      console.log('Framework ready hook failed, continuing...');
    }
  }, []);

  return (
    <SafeAreaProvider>
      <ThemeProvider>
        <AuthProvider>
          <FinanceProvider>
            <Stack 
              screenOptions={{ headerShown: false }}
            >
              <Stack.Screen name="index" options={{ gestureEnabled: false }} />
              <Stack.Screen name="(auth)" options={{ gestureEnabled: false }} />
              <Stack.Screen name="(onboarding)" options={{ gestureEnabled: false }} />
              <Stack.Screen name="(tabs)" options={{ gestureEnabled: false }} />
              <Stack.Screen name="+not-found" />
              <Stack.Screen name="payment-success" />
              <Stack.Screen name="payment" />
            </Stack>
            <StatusBar style="auto" />
          </FinanceProvider>
        </AuthProvider>
      </ThemeProvider>
    </SafeAreaProvider>
  );
}


export default RootLayout;