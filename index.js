// ULTRA-AGGRESSIVE PATCHING - Must happen BEFORE any imports
if (typeof global !== 'undefined') {
  // Completely disable the problematic observer
  global.HermesInternal = global.HermesInternal || {};
  
  // Override any screen capture related functionality at the lowest level
  const originalGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;
  Object.getOwnPropertyDescriptor = function(obj, prop) {
    if (prop === 'NativeUnimoduleProxy' || prop === 'registerScreenCaptureObserver') {
      return {
        value: {},
        writable: true,
        enumerable: false,
        configurable: true
      };
    }
    return originalGetOwnPropertyDescriptor.call(this, obj, prop);
  };
  
  // Patch console before any other code runs
  const originalError = console.error;
  const originalWarn = console.warn;
  const originalLog = console.log;
  
  console.error = (...args) => {
    const message = String(args[0] || '');
    if (message.includes('NativeUnimoduleProxy') || 
        message.includes('HostObject::get') ||
        message.includes('DETECT_SCREEN_CAPTURE') ||
        message.includes('registerScreenCaptureObserver')) {
      return; // Completely suppress
    }
    return originalError.apply(console, args);
  };
  
  console.warn = (...args) => {
    const message = String(args[0] || '');
    if (message.includes('NativeUnimoduleProxy') || message.includes('DETECT_SCREEN_CAPTURE')) {
      return;
    }
    return originalWarn.apply(console, args);
  };
}

import { AppRegistry, Platform } from 'react-native';

// Import your actual login screen directly, bypassing all routing
let LoginScreen;
try {
  LoginScreen = require('./app/(auth)/login').default;
} catch (e) {
  console.log('Could not load login screen, using fallback');
  // Fallback component
  const React = require('react');
  const { View, Text, StyleSheet } = require('react-native');
  
  LoginScreen = () => React.createElement(View, {
    style: { flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fff' }
  }, [
    React.createElement(Text, {
      key: 'title',
      style: { fontSize: 24, fontWeight: 'bold', marginBottom: 10 }
    }, 'DULU Finance Manager'),
    React.createElement(Text, {
      key: 'status',
      style: { fontSize: 16, textAlign: 'center' }
    }, 'App is working! Login screen loading...')
  ]);
}

// Force registration immediately with your login screen
AppRegistry.registerComponent('main', () => LoginScreen);
AppRegistry.registerComponent('bolt-expo-nativewind', () => LoginScreen);
AppRegistry.registerComponent('dulu-finance-manager', () => LoginScreen);

export default LoginScreen;