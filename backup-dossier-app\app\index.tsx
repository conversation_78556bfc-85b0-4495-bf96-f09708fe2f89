import { useEffect } from 'react';
import { Redirect } from 'expo-router';
import { Platform } from 'react-native';

export default function Index() {
  useEffect(() => {
    // Ensure redirection works correctly on mobile with additional safety
    if (Platform.OS !== 'web') {
      console.log('Mobile platform detected, redirecting to login...');
      // Additional safety timeout for mobile
      setTimeout(() => {
        console.log('Ensuring navigation to login');
      }, 100);
    }
  }, []);

  // Direct redirect to your login page
  return <Redirect href="/(auth)/login" />;
}