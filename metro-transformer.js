const { transform } = require('@expo/metro-config/build/transformer');

module.exports = {
  transform: async (config) => {
    const result = await transform(config);
    
    // Patch the code to avoid NativeUnimoduleProxy screen capture issues
    if (result.output && result.output[0] && result.output[0].data && result.output[0].data.code) {
      let code = result.output[0].data.code;
      
      // Replace problematic NativeUnimoduleProxy calls
      if (code.includes('registerScreenCaptureObserver') || code.includes('NativeUnimoduleProxy')) {
        code = code.replace(
          /registerScreenCaptureObserver/g, 
          '// registerScreenCaptureObserver - disabled for mobile compatibility'
        );
        
        // Wrap NativeUnimoduleProxy calls in try-catch
        code = code.replace(
          /NativeUnimoduleProxy\./g,
          '(typeof NativeUnimoduleProxy !== "undefined" ? NativeUnimoduleProxy : {}).'
        );
      }
      
      result.output[0].data.code = code;
    }
    
    return result;
  },
};