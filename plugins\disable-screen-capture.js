const { withAndroidManifest } = require('@expo/config-plugins');

function withDisableScreenCapture(config) {
  return withAndroidManifest(config, (config) => {
    const androidManifest = config.modResults;
    
    // Remove any auto-generated screen capture permissions
    if (androidManifest.manifest && androidManifest.manifest['uses-permission']) {
      androidManifest.manifest['uses-permission'] = androidManifest.manifest['uses-permission'].filter(
        permission => permission.$['android:name'] !== 'android.permission.DETECT_SCREEN_CAPTURE'
      );
    }
    
    return config;
  });
}

module.exports = withDisableScreenCapture;